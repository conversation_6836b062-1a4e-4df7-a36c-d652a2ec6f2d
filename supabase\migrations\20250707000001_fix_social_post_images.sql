-- Fix social post images to follow avatar pattern instead of product images pattern
-- This migration removes the JSONB column and adds individual URL and key columns

-- Remove the JSONB column that was added in the previous migration
ALTER TABLE public.social_posts 
DROP COLUMN IF EXISTS post_images;

-- Add the new image columns (following avatar pattern)
ALTER TABLE public.social_posts 
ADD COLUMN image_url text,
ADD COLUMN image_cloudflare_key text;

-- Add comments for documentation
COMMENT ON COLUMN public.social_posts.image_url IS 'Public URL for the post image';
COMMENT ON COLUMN public.social_posts.image_cloudflare_key IS 'Cloudflare R2 key for image deletion - handled directly in application';
