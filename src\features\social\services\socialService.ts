import { supabase } from '@/integrations/supabase/client';
import type {
  SocialPost,
  SocialComment,
  SocialFollow,
  SocialPostWithAuthor,
  SocialCommentWithAuthor,
  SocialPostReaction,
  NewSocialPost,
  NewSocialComment,
  NewSocialFollow,
  NewSocialPostReaction,
  PostFormData,
  CommentFormData,
  FeedFilters,
  PaginationParams,
  PaginatedResponse,
  UserConnection,
  ConnectionStats,
  ReactionType,
  PostReactions,
  PostImage
} from '../types/social.types';

export class SocialService {
  /**
   * Parse post images from JSON string
   */
  private static parsePostImages(postImagesJson: any): PostImage[] {
    if (!postImagesJson) return [];

    try {
      if (typeof postImagesJson === 'string') {
        return JSON.parse(postImagesJson);
      }
      if (Array.isArray(postImagesJson)) {
        return postImagesJson;
      }
      return [];
    } catch (error) {
      console.error('Error parsing post images:', error);
      return [];
    }
  }

  /**
   * Create a new social post
   */
  static async createPost(data: PostFormData): Promise<SocialPost> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User must be authenticated to create posts');
    }

    // Create the post
    const { data: post, error: postError } = await supabase
      .from('social_posts')
      .insert({
        user_id: user.id,
        content: data.content.trim(),
        post_images: data.images ? JSON.stringify(data.images) : '[]'
      })
      .select()
      .single();

    if (postError) {
      throw new Error(`Failed to create post: ${postError.message}`);
    }

    // Add net-zero categories if provided
    if (data.netZeroCategoryIds.length > 0) {
      const categoryInserts = data.netZeroCategoryIds.map(categoryId => ({
        post_id: post.id,
        subcategory_id: categoryId
      }));

      const { error: categoryError } = await supabase
        .from('social_post_netzero_categories')
        .insert(categoryInserts);

      if (categoryError) {
        console.error('Failed to add categories:', categoryError);
      }
    }

    // Add industry tags if provided
    if (data.industryIds.length > 0) {
      const industryInserts = data.industryIds.map(industryId => ({
        post_id: post.id,
        industry_id: industryId
      }));

      const { error: industryError } = await supabase
        .from('social_post_industries')
        .insert(industryInserts);

      if (industryError) {
        console.error('Failed to add industries:', industryError);
      }
    }

    return post;
  }

  /**
   * Get social feed with filtering and pagination
   */
  static async getFeed(
    filters: FeedFilters,
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<SocialPostWithAuthor>> {
    const { limit = 20, cursor } = pagination;
    const { data: { user } } = await supabase.auth.getUser();

    let query = supabase
      .from('social_posts')
      .select(`
        *,
        author:profiles!social_posts_user_id_fkey (
          id,
          first_name,
          last_name,
          avatar_url,
          job_title,
          organisation_name
        ),
        categories:social_post_netzero_categories (
          subcategory:netzero_subcategories (
            id,
            name,
            category:netzero_categories (
              name
            )
          )
        ),
        industries:social_post_industries (
          industry:uk_industries_with_parent (
            id,
            name,
            parent_name
          )
        )
      `)
      .eq('is_deleted', false)
      .order('created_at', { ascending: filters.sort === 'oldest' })
      .limit(limit + 1); // Get one extra to check if there are more

    // Apply following filter if user is authenticated and filter is set
    if (filters.filter === 'following' && user) {
      const { data: followingIds } = await supabase
        .from('social_follows')
        .select('following_id')
        .eq('follower_id', user.id);

      if (followingIds && followingIds.length > 0) {
        const ids = followingIds.map(f => f.following_id);
        query = query.in('user_id', ids);
      } else {
        // User follows no one, return empty result
        return { data: [], hasMore: false };
      }
    }

    // Apply category filters if provided
    if (filters.categoryIds && filters.categoryIds.length > 0) {
      const { data: postIdsWithCategories } = await supabase
        .from('social_post_netzero_categories')
        .select('post_id')
        .in('subcategory_id', filters.categoryIds);

      if (postIdsWithCategories && postIdsWithCategories.length > 0) {
        const postIds = postIdsWithCategories.map(p => p.post_id);
        query = query.in('id', postIds);
      } else {
        // No posts match the category filter, return empty result
        return { data: [], hasMore: false };
      }
    }

    // Apply industry filters if provided
    if (filters.industryIds && filters.industryIds.length > 0) {
      const { data: postIdsWithIndustries } = await supabase
        .from('social_post_industries')
        .select('post_id')
        .in('industry_id', filters.industryIds);

      if (postIdsWithIndustries && postIdsWithIndustries.length > 0) {
        const postIds = postIdsWithIndustries.map(p => p.post_id);

        // If we already have category filtering, we need to intersect the results
        if (filters.categoryIds && filters.categoryIds.length > 0) {
          // Get the current query's post IDs and intersect with industry post IDs
          const currentQuery = supabase
            .from('social_posts')
            .select('id')
            .eq('is_deleted', false);

          // Apply the same filters that were applied to the main query
          if (filters.filter === 'following' && user) {
            const { data: followingIds } = await supabase
              .from('social_follows')
              .select('following_id')
              .eq('follower_id', user.id);

            if (followingIds && followingIds.length > 0) {
              const ids = followingIds.map(f => f.following_id);
              currentQuery.in('user_id', ids);
            }
          }

          // Apply category filter to get current post IDs
          const { data: categoryPostIds } = await supabase
            .from('social_post_netzero_categories')
            .select('post_id')
            .in('subcategory_id', filters.categoryIds);

          if (categoryPostIds && categoryPostIds.length > 0) {
            const categoryIds = categoryPostIds.map(p => p.post_id);
            const intersectedIds = postIds.filter(id => categoryIds.includes(id));

            if (intersectedIds.length > 0) {
              query = query.in('id', intersectedIds);
            } else {
              // No posts match both category and industry filters
              return { data: [], hasMore: false };
            }
          } else {
            return { data: [], hasMore: false };
          }
        } else {
          query = query.in('id', postIds);
        }
      } else {
        // No posts match the industry filter, return empty result
        return { data: [], hasMore: false };
      }
    }

    // Apply cursor-based pagination
    if (cursor) {
      const operator = filters.sort === 'oldest' ? 'gt' : 'lt';
      query = query[operator]('created_at', cursor);
    }

    const { data: posts, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch feed: ${error.message}`);
    }

    const hasMore = posts.length > limit;
    const resultPosts = hasMore ? posts.slice(0, limit) : posts;

    // Get user reactions for all posts if user is authenticated
    let userReactionsMap: Map<string, ReactionType[]> = new Map();
    if (user && resultPosts.length > 0) {
      const postIds = resultPosts.map(p => p.id);
      const { data: userReactions } = await supabase
        .from('social_post_reactions')
        .select('post_id, reaction_type')
        .eq('user_id', user.id)
        .in('post_id', postIds);

      if (userReactions) {
        userReactions.forEach(reaction => {
          const existing = userReactionsMap.get(reaction.post_id) || [];
          existing.push(reaction.reaction_type as ReactionType);
          userReactionsMap.set(reaction.post_id, existing);
        });
      }
    }

    // Transform the data to match our expected format
    const transformedPosts: SocialPostWithAuthor[] = resultPosts.map(post => ({
      ...post,
      author: post.author,
      post_images: this.parsePostImages(post.post_images),
      categories: post.categories?.map(c => ({
        id: c.subcategory.id,
        name: c.subcategory.name,
        category: {
          name: c.subcategory.category.name
        }
      })) || [],
      industries: post.industries?.map(i => ({
        id: i.industry.id,
        name: i.industry.name,
        parent: i.industry.parent_name ? {
          name: i.industry.parent_name
        } : undefined
      })) || [],
      user_reactions: userReactionsMap.get(post.id) || []
    }));

    return {
      data: transformedPosts,
      hasMore,
      nextCursor: hasMore ? resultPosts[resultPosts.length - 1].created_at : undefined
    };
  }

  /**
   * Get a single post with full details
   */
  static async getPost(postId: string): Promise<SocialPostWithAuthor> {
    const { data: post, error } = await supabase
      .from('social_posts')
      .select(`
        *,
        author:profiles!social_posts_user_id_fkey (
          id,
          first_name,
          last_name,
          avatar_url,
          job_title,
          organisation_name
        ),
        categories:social_post_netzero_categories (
          subcategory:netzero_subcategories (
            id,
            name,
            category:netzero_categories (
              name
            )
          )
        ),
        industries:social_post_industries (
          industry:uk_industries_with_parent (
            id,
            name,
            parent_name
          )
        )
      `)
      .eq('id', postId)
      .eq('is_deleted', false)
      .single();

    if (error) {
      throw new Error(`Failed to fetch post: ${error.message}`);
    }

    return {
      ...post,
      author: post.author,
      post_images: this.parsePostImages(post.post_images),
      categories: post.categories?.map(c => ({
        id: c.subcategory.id,
        name: c.subcategory.name,
        category: {
          name: c.subcategory.category.name
        }
      })) || [],
      industries: post.industries?.map(i => ({
        id: i.industry.id,
        name: i.industry.name,
        parent: i.industry.parent_name ? {
          name: i.industry.parent_name
        } : undefined
      })) || []
    };
  }

  /**
   * Update a post
   */
  static async updatePost(postId: string, data: Partial<PostFormData>): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User must be authenticated to update posts');
    }

    // Update the post content if provided
    if (data.content !== undefined) {
      const { error: updateError } = await supabase
        .from('social_posts')
        .update({
          content: data.content.trim(),
          edited_at: new Date().toISOString()
        })
        .eq('id', postId)
        .eq('user_id', user.id);

      if (updateError) {
        throw new Error(`Failed to update post: ${updateError.message}`);
      }
    }

    // Update categories if provided
    if (data.netZeroCategoryIds !== undefined) {
      // Remove existing categories
      await supabase
        .from('social_post_netzero_categories')
        .delete()
        .eq('post_id', postId);

      // Add new categories
      if (data.netZeroCategoryIds.length > 0) {
        const categoryInserts = data.netZeroCategoryIds.map(categoryId => ({
          post_id: postId,
          subcategory_id: categoryId
        }));

        await supabase
          .from('social_post_netzero_categories')
          .insert(categoryInserts);
      }
    }

    // Update industries if provided
    if (data.industryIds !== undefined) {
      // Remove existing industries
      await supabase
        .from('social_post_industries')
        .delete()
        .eq('post_id', postId);

      // Add new industries
      if (data.industryIds.length > 0) {
        const industryInserts = data.industryIds.map(industryId => ({
          post_id: postId,
          industry_id: industryId
        }));

        await supabase
          .from('social_post_industries')
          .insert(industryInserts);
      }
    }
  }

  /**
   * Delete a post (soft delete)
   */
  static async deletePost(postId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to delete posts');
    }

    const { error } = await supabase
      .from('social_posts')
      .update({ is_deleted: true })
      .eq('id', postId)
      .eq('user_id', user.id);

    if (error) {
      throw new Error(`Failed to delete post: ${error.message}`);
    }
  }

  /**
   * Create a comment or reply
   */
  static async createComment(
    postId: string,
    data: CommentFormData,
    parentCommentId?: string
  ): Promise<SocialComment> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to create comments');
    }

    const { data: comment, error } = await supabase
      .from('social_comments')
      .insert({
        post_id: postId,
        user_id: user.id,
        parent_comment_id: parentCommentId || null,
        content: data.content.trim()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create comment: ${error.message}`);
    }

    return comment;
  }

  /**
   * Get comments for a post with nested replies
   */
  static async getComments(
    postId: string,
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<SocialCommentWithAuthor>> {
    const { limit = 20, cursor } = pagination;

    let query = supabase
      .from('social_comments')
      .select(`
        *,
        author:profiles!social_comments_user_id_fkey (
          id,
          first_name,
          last_name,
          avatar_url,
          job_title,
          organisation_name
        )
      `)
      .eq('post_id', postId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: true })
      .limit(limit + 1);

    if (cursor) {
      query = query.gt('created_at', cursor);
    }

    const { data: comments, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch comments: ${error.message}`);
    }

    const hasMore = comments.length > limit;
    const resultComments = hasMore ? comments.slice(0, limit) : comments;

    // Build nested comment tree
    const commentMap = new Map<string, SocialCommentWithAuthor>();
    const rootComments: SocialCommentWithAuthor[] = [];

    // First pass: create all comment objects
    resultComments.forEach(comment => {
      const commentWithAuthor: SocialCommentWithAuthor = {
        ...comment,
        author: comment.author,
        replies: []
      };
      commentMap.set(comment.id, commentWithAuthor);
    });

    // Second pass: build the tree structure
    resultComments.forEach(comment => {
      const commentWithAuthor = commentMap.get(comment.id)!;

      if (comment.parent_comment_id) {
        const parent = commentMap.get(comment.parent_comment_id);
        if (parent) {
          parent.replies = parent.replies || [];
          parent.replies.push(commentWithAuthor);
        }
      } else {
        rootComments.push(commentWithAuthor);
      }
    });

    return {
      data: rootComments,
      hasMore,
      nextCursor: hasMore ? resultComments[resultComments.length - 1].created_at : undefined
    };
  }

  /**
   * Update a comment
   */
  static async updateComment(commentId: string, content: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to update comments');
    }

    const { error } = await supabase
      .from('social_comments')
      .update({
        content: content.trim(),
        edited_at: new Date().toISOString()
      })
      .eq('id', commentId)
      .eq('user_id', user.id);

    if (error) {
      throw new Error(`Failed to update comment: ${error.message}`);
    }
  }

  /**
   * Delete a comment (soft delete)
   */
  static async deleteComment(commentId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to delete comments');
    }

    const { error } = await supabase
      .from('social_comments')
      .update({ is_deleted: true })
      .eq('id', commentId)
      .eq('user_id', user.id);

    if (error) {
      throw new Error(`Failed to delete comment: ${error.message}`);
    }
  }

  /**
   * Follow a user
   */
  static async followUser(userId: string): Promise<SocialFollow> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to follow others');
    }

    if (user.id === userId) {
      throw new Error('Cannot follow yourself');
    }

    const { data: follow, error } = await supabase
      .from('social_follows')
      .insert({
        follower_id: user.id,
        following_id: userId
      })
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        throw new Error('Already following this user');
      }
      throw new Error(`Failed to follow user: ${error.message}`);
    }

    return follow;
  }

  /**
   * Unfollow a user
   */
  static async unfollowUser(userId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to unfollow others');
    }

    const { error } = await supabase
      .from('social_follows')
      .delete()
      .eq('follower_id', user.id)
      .eq('following_id', userId);

    if (error) {
      throw new Error(`Failed to unfollow user: ${error.message}`);
    }
  }

  /**
   * Check if current user is following another user
   */
  static async isFollowing(userId: string): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user || user.id === userId) {
      return false;
    }

    const { data, error } = await supabase
      .from('social_follows')
      .select('id')
      .eq('follower_id', user.id)
      .eq('following_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = not found
      throw new Error(`Failed to check follow status: ${error.message}`);
    }

    return !!data;
  }

  /**
   * Get user's followers
   */
  static async getFollowers(
    userId: string,
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<UserConnection>> {
    const { limit = 20, cursor } = pagination;

    let query = supabase
      .from('social_follows')
      .select(`
        created_at,
        follower:profiles!social_follows_follower_id_fkey (
          id,
          first_name,
          last_name,
          avatar_url,
          job_title,
          organisation_name
        )
      `)
      .eq('following_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit + 1);

    if (cursor) {
      query = query.lt('created_at', cursor);
    }

    const { data: follows, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch followers: ${error.message}`);
    }

    const hasMore = follows.length > limit;
    const resultFollows = hasMore ? follows.slice(0, limit) : follows;

    const followers: UserConnection[] = resultFollows.map(follow => ({
      ...follow.follower,
      followed_at: follow.created_at
    }));

    return {
      data: followers,
      hasMore,
      nextCursor: hasMore ? resultFollows[resultFollows.length - 1].created_at : undefined
    };
  }

  /**
   * Get users that a user is following
   */
  static async getFollowing(
    userId: string,
    pagination: PaginationParams = {}
  ): Promise<PaginatedResponse<UserConnection>> {
    const { limit = 20, cursor } = pagination;

    let query = supabase
      .from('social_follows')
      .select(`
        created_at,
        following:profiles!social_follows_following_id_fkey (
          id,
          first_name,
          last_name,
          avatar_url,
          job_title,
          organisation_name
        )
      `)
      .eq('follower_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit + 1);

    if (cursor) {
      query = query.lt('created_at', cursor);
    }

    const { data: follows, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch following: ${error.message}`);
    }

    const hasMore = follows.length > limit;
    const resultFollows = hasMore ? follows.slice(0, limit) : follows;

    const following: UserConnection[] = resultFollows.map(follow => ({
      ...follow.following,
      followed_at: follow.created_at
    }));

    return {
      data: following,
      hasMore,
      nextCursor: hasMore ? resultFollows[resultFollows.length - 1].created_at : undefined
    };
  }

  /**
   * Get connection stats for a user
   */
  static async getConnectionStats(userId: string): Promise<ConnectionStats> {
    const [followersResult, followingResult] = await Promise.all([
      supabase
        .from('social_follows')
        .select('id', { count: 'exact', head: true })
        .eq('following_id', userId),
      supabase
        .from('social_follows')
        .select('id', { count: 'exact', head: true })
        .eq('follower_id', userId)
    ]);

    if (followersResult.error) {
      throw new Error(`Failed to fetch follower count: ${followersResult.error.message}`);
    }

    if (followingResult.error) {
      throw new Error(`Failed to fetch following count: ${followingResult.error.message}`);
    }

    return {
      follower_count: followersResult.count || 0,
      following_count: followingResult.count || 0
    };
  }

  /**
   * Add or update a reaction to a post
   */
  static async addReaction(postId: string, reactionType: ReactionType): Promise<SocialPostReaction> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to react to posts');
    }

    const { data: reaction, error } = await supabase
      .from('social_post_reactions')
      .upsert({
        post_id: postId,
        user_id: user.id,
        reaction_type: reactionType
      }, {
        onConflict: 'post_id,user_id,reaction_type'
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to add reaction: ${error.message}`);
    }

    return reaction;
  }

  /**
   * Remove a reaction from a post
   */
  static async removeReaction(postId: string, reactionType: ReactionType): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to remove reactions');
    }

    const { error } = await supabase
      .from('social_post_reactions')
      .delete()
      .eq('post_id', postId)
      .eq('user_id', user.id)
      .eq('reaction_type', reactionType);

    if (error) {
      throw new Error(`Failed to remove reaction: ${error.message}`);
    }
  }

  /**
   * Toggle a reaction on a post (add if not present, remove if present)
   */
  static async toggleReaction(postId: string, reactionType: ReactionType): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to react to posts');
    }

    // Check if user already has this reaction
    const { data: existingReaction } = await supabase
      .from('social_post_reactions')
      .select('id')
      .eq('post_id', postId)
      .eq('user_id', user.id)
      .eq('reaction_type', reactionType)
      .single();

    if (existingReaction) {
      // Remove the reaction
      await this.removeReaction(postId, reactionType);
      return false;
    } else {
      // Add the reaction
      await this.addReaction(postId, reactionType);
      return true;
    }
  }

  /**
   * Get user's reactions for a specific post
   */
  static async getUserReactions(postId: string): Promise<ReactionType[]> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return [];
    }

    const { data: reactions, error } = await supabase
      .from('social_post_reactions')
      .select('reaction_type')
      .eq('post_id', postId)
      .eq('user_id', user.id);

    if (error) {
      console.error('Failed to fetch user reactions:', error);
      return [];
    }

    return reactions.map(r => r.reaction_type as ReactionType);
  }

  /**
   * Get reaction summary for a post
   */
  static async getPostReactions(postId: string): Promise<PostReactions> {
    const { data: { user } } = await supabase.auth.getUser();

    // Get the post with reaction counts
    const { data: post, error: postError } = await supabase
      .from('social_posts')
      .select('like_count, support_count, love_count, insightful_count, total_reaction_count')
      .eq('id', postId)
      .single();

    if (postError) {
      throw new Error(`Failed to fetch post reactions: ${postError.message}`);
    }

    // Get user's reactions if authenticated
    let userReactions: ReactionType[] = [];
    if (user) {
      userReactions = await this.getUserReactions(postId);
    }

    return {
      like: {
        type: 'like',
        count: post.like_count || 0,
        userHasReacted: userReactions.includes('like')
      },
      support: {
        type: 'support',
        count: post.support_count || 0,
        userHasReacted: userReactions.includes('support')
      },
      love: {
        type: 'love',
        count: post.love_count || 0,
        userHasReacted: userReactions.includes('love')
      },
      insightful: {
        type: 'insightful',
        count: post.insightful_count || 0,
        userHasReacted: userReactions.includes('insightful')
      },
      total: post.total_reaction_count || 0
    };
  }

  // ==========================================
  // COMMENT REACTIONS
  // ==========================================

  /**
   * Add a reaction to a comment
   */
  static async addCommentReaction(commentId: string, reactionType: ReactionType): Promise<any> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to react to comments');
    }

    const { data: reaction, error } = await supabase
      .from('social_comment_reactions')
      .upsert({
        comment_id: commentId,
        user_id: user.id,
        reaction_type: reactionType
      }, {
        onConflict: 'comment_id,user_id,reaction_type'
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to add comment reaction: ${error.message}`);
    }

    return reaction;
  }

  /**
   * Remove a reaction from a comment
   */
  static async removeCommentReaction(commentId: string, reactionType: ReactionType): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to remove comment reactions');
    }

    const { error } = await supabase
      .from('social_comment_reactions')
      .delete()
      .eq('comment_id', commentId)
      .eq('user_id', user.id)
      .eq('reaction_type', reactionType);

    if (error) {
      throw new Error(`Failed to remove comment reaction: ${error.message}`);
    }
  }

  /**
   * Toggle a reaction on a comment (add if not present, remove if present)
   */
  static async toggleCommentReaction(commentId: string, reactionType: ReactionType): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to react to comments');
    }

    // Check if user already has this reaction
    const { data: existingReaction } = await supabase
      .from('social_comment_reactions')
      .select('id')
      .eq('comment_id', commentId)
      .eq('user_id', user.id)
      .eq('reaction_type', reactionType)
      .single();

    if (existingReaction) {
      // Remove the reaction
      await this.removeCommentReaction(commentId, reactionType);
      return false;
    } else {
      // Add the reaction
      await this.addCommentReaction(commentId, reactionType);
      return true;
    }
  }

  /**
   * Get user's reactions for a specific comment
   */
  static async getUserCommentReactions(commentId: string): Promise<ReactionType[]> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return [];
    }

    const { data: reactions, error } = await supabase
      .from('social_comment_reactions')
      .select('reaction_type')
      .eq('comment_id', commentId)
      .eq('user_id', user.id);

    if (error) {
      console.error('Failed to fetch user comment reactions:', error);
      return [];
    }

    return reactions.map(r => r.reaction_type as ReactionType);
  }
}
