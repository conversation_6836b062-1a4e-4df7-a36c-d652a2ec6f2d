import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

interface UploadResult {
  url: string;
  key: string;
}

export class CloudflareService {
  private s3Client: S3Client;
  private bucketName: string;
  private publicUrl: string;

  constructor() {
    // Initialize S3 client with Cloudflare R2 configuration
    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: import.meta.env.VITE_CLOUDFLARE_R2_ENDPOINT,
      credentials: {
        accessKeyId: import.meta.env.VITE_CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: import.meta.env.VITE_CLOUDFLARE_R2_SECRET_ACCESS_KEY,
      },
    });

    this.bucketName = import.meta.env.VITE_CLOUDFLARE_R2_BUCKET_NAME;
    this.publicUrl = import.meta.env.VITE_CLOUDFLARE_PUBLIC_URL;

    // Validate environment variables
    this.validateConfig();
  }

  private validateConfig(): void {
    const requiredEnvVars = [
      'VITE_CLOUDFLARE_R2_ENDPOINT',
      'VITE_CLOUDFLARE_R2_ACCESS_KEY_ID',
      'VITE_CLOUDFLARE_R2_SECRET_ACCESS_KEY',
      'VITE_CLOUDFLARE_R2_BUCKET_NAME',
      'VITE_CLOUDFLARE_PUBLIC_URL',
    ];

    const missingVars = requiredEnvVars.filter(
      (varName) => !import.meta.env[varName]
    );

    if (missingVars.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missingVars.join(', ')}`
      );
    }
  }

  /**
   * Upload an avatar file to Cloudflare R2
   * @param file - The file to upload
   * @param userId - The user ID for organizing files
   * @returns Promise with the public URL and key for deletion
   */
  async uploadAvatar(file: File, userId: string): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file);

      // Generate unique key for the file
      const key = this.generateAvatarKey(userId, file.name);

      // Convert file to buffer
      const buffer = await file.arrayBuffer();

      // Create upload command
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: new Uint8Array(buffer),
        ContentType: file.type,
        Metadata: {
          userId: userId,
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
        },
      });

      // Upload to Cloudflare R2
      await this.s3Client.send(command);

      // Generate public URL
      const url = `${this.publicUrl}/${key}`;

      return {
        url,
        key,
      };
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw new Error(`Failed to upload avatar: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload a business logo to Cloudflare R2
   * @param file - The file to upload
   * @param businessId - The business ID for organizing files
   * @returns Promise with the public URL and key for deletion
   */
  async uploadBusinessLogo(file: File, businessId: string): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file);

      // Generate unique key for the file
      const key = this.generateBusinessLogoKey(businessId, file.name);

      // Convert file to buffer
      const buffer = await file.arrayBuffer();

      // Create upload command
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: new Uint8Array(buffer),
        ContentType: file.type,
        Metadata: {
          businessId: businessId,
          imageType: 'logo',
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
        },
      });

      // Upload to Cloudflare R2
      await this.s3Client.send(command);

      // Generate public URL
      const url = `${this.publicUrl}/${key}`;

      return {
        url,
        key,
      };
    } catch (error) {
      console.error('Error uploading business logo:', error);
      throw new Error(`Failed to upload business logo: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload a product image to Cloudflare R2
   * @param file - The file to upload
   * @param businessId - The business ID for organizing files
   * @returns Promise with the public URL and key for deletion
   */
  async uploadProductImage(file: File, businessId: string): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file);

      // Generate unique key for the file
      const key = this.generateProductImageKey(businessId, file.name);

      // Convert file to buffer
      const buffer = await file.arrayBuffer();

      // Create upload command
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: new Uint8Array(buffer),
        ContentType: file.type,
        Metadata: {
          businessId: businessId,
          imageType: 'product',
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
        },
      });

      // Upload to Cloudflare R2
      await this.s3Client.send(command);

      // Generate public URL
      const url = `${this.publicUrl}/${key}`;

      return {
        url,
        key,
      };
    } catch (error) {
      console.error('Error uploading product image:', error);
      throw new Error(`Failed to upload product image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete an avatar from Cloudflare R2
   * @param key - The key/path of the file to delete
   * @returns Promise<boolean> - true if successful, false otherwise
   */
  async deleteAvatar(key: string): Promise<boolean> {
    try {
      if (!key || key.trim() === '') {
        console.warn('Attempted to delete avatar with empty key');
        return false;
      }

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      console.log(`Successfully deleted avatar: ${key}`);
      return true;
    } catch (error) {
      console.error('Error deleting avatar:', error);
      // Don't throw error for deletion failures - log and return false
      // This prevents blocking other operations when cleanup fails
      return false;
    }
  }

  /**
   * Delete a business image from Cloudflare R2
   * @param key - The key/path of the file to delete
   * @returns Promise<boolean> - true if successful, false otherwise
   */
  async deleteBusinessImage(key: string): Promise<boolean> {
    try {
      if (!key || key.trim() === '') {
        console.warn('Attempted to delete business image with empty key');
        return false;
      }

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      console.log(`Successfully deleted business image: ${key}`);
      return true;
    } catch (error) {
      console.error('Error deleting business image:', error);
      // Don't throw error for deletion failures - log and return false
      // This prevents blocking other operations when cleanup fails
      return false;
    }
  }

  /**
   * Upload a social post image to Cloudflare R2
   * @param file - The file to upload
   * @param userId - The user ID for organizing files
   * @returns Promise with the public URL and key for deletion
   */
  async uploadSocialPostImage(file: File, userId: string): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file);

      // Generate unique key for the file
      const key = this.generateSocialPostImageKey(userId, file.name);

      // Convert file to buffer
      const buffer = await file.arrayBuffer();

      // Create upload command
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: new Uint8Array(buffer),
        ContentType: file.type,
        Metadata: {
          userId: userId,
          imageType: 'social-post',
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
        },
      });

      // Upload to Cloudflare R2
      await this.s3Client.send(command);

      // Generate public URL
      const url = `${this.publicUrl}/${key}`;

      return {
        url,
        key,
      };
    } catch (error) {
      console.error('Error uploading social post image:', error);
      throw new Error(`Failed to upload social post image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a social post image from Cloudflare R2
   * @param key - The key/path of the file to delete
   * @returns Promise<boolean> - true if successful, false otherwise
   */
  async deleteSocialPostImage(key: string): Promise<boolean> {
    try {
      if (!key || key.trim() === '') {
        console.warn('Attempted to delete social post image with empty key');
        return false;
      }

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      console.log(`Successfully deleted social post image: ${key}`);
      return true;
    } catch (error) {
      console.error('Error deleting social post image:', error);
      // Don't throw error for deletion failures - log and return false
      // This prevents blocking other operations when cleanup fails
      return false;
    }
  }

  /**
   * Generate a unique key for storing the avatar
   * @param userId - The user ID
   * @param filename - Original filename
   * @returns Unique key for the file
   */
  private generateAvatarKey(userId: string, filename: string): string {
    // Extract file extension
    const extension = this.getFileExtension(filename);
    
    // Generate timestamp for uniqueness
    const timestamp = Date.now();
    
    // Create organized path: avatars/userId/timestamp.extension
    return `avatars/${userId}/${timestamp}${extension}`;
  }

  /**
   * Generate a unique key for storing business logos
   * @param businessId - The business ID
   * @param filename - Original filename
   * @returns Unique key for the file
   */
  private generateBusinessLogoKey(businessId: string, filename: string): string {
    // Extract file extension
    const extension = this.getFileExtension(filename);
    
    // Generate timestamp for uniqueness
    const timestamp = Date.now();
    
    // Create organized path: business-logos/businessId/timestamp.extension
    return `business-logos/${businessId}/${timestamp}${extension}`;
  }

  /**
   * Generate a unique key for storing product images
   * @param businessId - The business ID
   * @param filename - Original filename
   * @returns Unique key for the file
   */
  private generateProductImageKey(businessId: string, filename: string): string {
    // Extract file extension
    const extension = this.getFileExtension(filename);

    // Generate timestamp for uniqueness
    const timestamp = Date.now();

    // Create organized path: business-products/businessId/timestamp.extension
    return `business-products/${businessId}/${timestamp}${extension}`;
  }

  /**
   * Generate a unique key for storing social post images
   * @param userId - The user ID
   * @param filename - Original filename
   * @returns Unique key for the file
   */
  private generateSocialPostImageKey(userId: string, filename: string): string {
    // Extract file extension
    const extension = this.getFileExtension(filename);

    // Generate timestamp for uniqueness
    const timestamp = Date.now();

    // Create organized path: social-posts/userId/timestamp.extension
    return `social-posts/${userId}/${timestamp}${extension}`;
  }

  /**
   * Get file extension from filename
   * @param filename - The filename
   * @returns File extension with dot (e.g., '.jpg')
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex > 0 ? filename.substring(lastDotIndex).toLowerCase() : '';
  }

  /**
   * Validate uploaded file
   * @param file - The file to validate
   */
  private validateFile(file: File): void {
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      throw new Error('File size must be less than 5MB');
    }

    // Check file type
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/gif'
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error('File type must be JPEG, PNG, WebP, or GIF');
    }

    // Check file extension as additional security
    const extension = this.getFileExtension(file.name);
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    
    if (!allowedExtensions.includes(extension)) {
      throw new Error('Invalid file extension');
    }
  }

  /**
   * Check if the service is properly configured
   * @returns boolean indicating if service is ready
   */
  isConfigured(): boolean {
    try {
      this.validateConfig();
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const cloudflareService = new CloudflareService();
