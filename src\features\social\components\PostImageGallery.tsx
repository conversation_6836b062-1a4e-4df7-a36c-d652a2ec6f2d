import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { X, ChevronLeft, ChevronRight, ZoomIn } from 'lucide-react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import type { PostImage } from '../types/social.types';

interface PostImageGalleryProps {
  images: PostImage[];
  className?: string;
}

export default function PostImageGallery({ images, className = '' }: PostImageGalleryProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  if (!images || images.length === 0) {
    return null;
  }

  const openLightbox = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeLightbox = () => {
    setSelectedImageIndex(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (selectedImageIndex === null) return;
    
    if (direction === 'prev') {
      setSelectedImageIndex(selectedImageIndex > 0 ? selectedImageIndex - 1 : images.length - 1);
    } else {
      setSelectedImageIndex(selectedImageIndex < images.length - 1 ? selectedImageIndex + 1 : 0);
    }
  };

  const renderImageGrid = () => {
    if (images.length === 1) {
      return (
        <div className="relative">
          <img
            src={images[0].url}
            alt={images[0].description || images[0].filename}
            className="w-full h-64 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => openLightbox(0)}
          />
          <Button
            variant="secondary"
            size="sm"
            className="absolute top-2 right-2 h-8 w-8 p-0 bg-black/50 hover:bg-black/70 text-white"
            onClick={() => openLightbox(0)}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          {images[0].description && (
            <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-sm p-2 rounded-b-lg">
              {images[0].description}
            </div>
          )}
        </div>
      );
    }

    if (images.length === 2) {
      return (
        <div className="grid grid-cols-2 gap-2">
          {images.map((image, index) => (
            <div key={image.id} className="relative">
              <img
                src={image.url}
                alt={image.description || image.filename}
                className="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => openLightbox(index)}
              />
              <Button
                variant="secondary"
                size="sm"
                className="absolute top-2 right-2 h-6 w-6 p-0 bg-black/50 hover:bg-black/70 text-white"
                onClick={() => openLightbox(index)}
              >
                <ZoomIn className="h-3 w-3" />
              </Button>
              {image.description && (
                <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 rounded-b-lg">
                  {image.description}
                </div>
              )}
            </div>
          ))}
        </div>
      );
    }

    if (images.length === 3) {
      return (
        <div className="grid grid-cols-2 gap-2">
          <div className="relative">
            <img
              src={images[0].url}
              alt={images[0].description || images[0].filename}
              className="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => openLightbox(0)}
            />
            <Button
              variant="secondary"
              size="sm"
              className="absolute top-2 right-2 h-6 w-6 p-0 bg-black/50 hover:bg-black/70 text-white"
              onClick={() => openLightbox(0)}
            >
              <ZoomIn className="h-3 w-3" />
            </Button>
            {images[0].description && (
              <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 rounded-b-lg">
                {images[0].description}
              </div>
            )}
          </div>
          <div className="grid grid-rows-2 gap-2">
            {images.slice(1, 3).map((image, index) => (
              <div key={image.id} className="relative">
                <img
                  src={image.url}
                  alt={image.description || image.filename}
                  className="w-full h-[94px] object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => openLightbox(index + 1)}
                />
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute top-1 right-1 h-5 w-5 p-0 bg-black/50 hover:bg-black/70 text-white"
                  onClick={() => openLightbox(index + 1)}
                >
                  <ZoomIn className="h-2 w-2" />
                </Button>
                {image.description && (
                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 rounded-b-lg truncate">
                    {image.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      );
    }

    // 4 or more images
    return (
      <div className="grid grid-cols-2 gap-2">
        {images.slice(0, 3).map((image, index) => (
          <div key={image.id} className="relative">
            <img
              src={image.url}
              alt={image.description || image.filename}
              className="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => openLightbox(index)}
            />
            <Button
              variant="secondary"
              size="sm"
              className="absolute top-1 right-1 h-5 w-5 p-0 bg-black/50 hover:bg-black/70 text-white"
              onClick={() => openLightbox(index)}
            >
              <ZoomIn className="h-2 w-2" />
            </Button>
            {image.description && (
              <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 rounded-b-lg truncate">
                {image.description}
              </div>
            )}
          </div>
        ))}
        <div className="relative">
          <img
            src={images[3].url}
            alt={images[3].description || images[3].filename}
            className="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => openLightbox(3)}
          />
          {images.length > 4 && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg cursor-pointer"
                 onClick={() => openLightbox(3)}>
              <span className="text-white text-lg font-semibold">+{images.length - 3}</span>
            </div>
          )}
          <Button
            variant="secondary"
            size="sm"
            className="absolute top-1 right-1 h-5 w-5 p-0 bg-black/50 hover:bg-black/70 text-white"
            onClick={() => openLightbox(3)}
          >
            <ZoomIn className="h-2 w-2" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={`mb-3 ${className}`}>
        {renderImageGrid()}
      </div>

      {/* Lightbox Dialog */}
      <Dialog open={selectedImageIndex !== null} onOpenChange={closeLightbox}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="p-4 pb-0">
            <DialogTitle className="flex items-center justify-between">
              <span>
                Image {selectedImageIndex !== null ? selectedImageIndex + 1 : 1} of {images.length}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={closeLightbox}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {selectedImageIndex !== null && (
            <div className="relative">
              <img
                src={images[selectedImageIndex].url}
                alt={images[selectedImageIndex].description || images[selectedImageIndex].filename}
                className="w-full max-h-[70vh] object-contain"
              />
              
              {/* Navigation buttons */}
              {images.length > 1 && (
                <>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 h-10 w-10 p-0 bg-black/50 hover:bg-black/70 text-white"
                    onClick={() => navigateImage('prev')}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 h-10 w-10 p-0 bg-black/50 hover:bg-black/70 text-white"
                    onClick={() => navigateImage('next')}
                  >
                    <ChevronRight className="h-5 w-5" />
                  </Button>
                </>
              )}
              
              {/* Image description */}
              {images[selectedImageIndex].description && (
                <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white p-4">
                  <p className="text-sm">{images[selectedImageIndex].description}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
