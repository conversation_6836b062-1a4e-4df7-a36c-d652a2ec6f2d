-- Add image support to social posts table
-- This migration adds post images support following the same pattern as avatar images

-- Add the new image columns (following avatar pattern)
ALTER TABLE public.social_posts
ADD COLUMN image_url text,
ADD COLUMN image_cloudflare_key text;

-- Add comments for documentation
COMMENT ON COLUMN public.social_posts.image_url IS 'Public URL for the post image';
COMMENT ON COLUMN public.social_posts.image_cloudflare_key IS 'Cloudflare R2 key for image deletion - handled directly in application';

-- Create social post image cleanup queue table for handling orphaned images
CREATE TABLE IF NOT EXISTS public.social_post_image_cleanup_queue (
  id SERIAL PRIMARY KEY,
  cloudflare_key TEXT NOT NULL,
  deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed BOOLEAN DEFAULT FALSE
);

-- Grant permissions on the cleanup queue table
GRANT ALL ON TABLE public.social_post_image_cleanup_queue TO anon;
GRANT ALL ON TABLE public.social_post_image_cleanup_queue TO authenticated;
GRANT ALL ON TABLE public.social_post_image_cleanup_queue TO service_role;
GRANT ALL ON SEQUENCE public.social_post_image_cleanup_queue_id_seq TO anon;
GRANT ALL ON SEQUENCE public.social_post_image_cleanup_queue_id_seq TO authenticated;
GRANT ALL ON SEQUENCE public.social_post_image_cleanup_queue_id_seq TO service_role;
