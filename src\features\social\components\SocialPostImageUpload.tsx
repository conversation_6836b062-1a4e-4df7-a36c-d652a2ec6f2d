import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, X, Loader2, Image as ImageIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cloudflareService } from '@/services/cloudflareService';

interface SocialPostImageUploadProps {
  imageUrl?: string;
  onImageChange: (imageUrl?: string, cloudflareKey?: string) => void;
  userId: string;
  disabled?: boolean;
}

export default function SocialPostImageUpload({
  imageUrl,
  onImageChange,
  userId,
  disabled = false
}: SocialPostImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(imageUrl);
  const { toast } = useToast();

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);

      // Create preview
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      toast({
        title: "Uploading...",
        description: "Your image is being uploaded to the cloud",
      });

      // Upload image to Cloudflare
      const { url, key } = await cloudflareService.uploadSocialPostImage(file, userId);

      // Clean up preview URL
      URL.revokeObjectURL(objectUrl);

      // Update with real URL
      setPreviewUrl(url);
      onImageChange(url, key);

      toast({
        title: "Image uploaded",
        description: "Your image has been added to the post",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload image",
        variant: "destructive"
      });
      // Reset preview on error
      setPreviewUrl(imageUrl);
    } finally {
      setUploading(false);
      // Reset the input
      event.target.value = '';
    }
  };

  const handleRemoveImage = () => {
    setPreviewUrl(undefined);
    onImageChange(undefined, undefined);

    toast({
      title: "Image removed",
      description: "The image has been removed from your post",
    });
  };

  return (
    <div className="space-y-4">
      {/* Current Image Display */}
      {previewUrl && (
        <Card className="relative">
          <CardContent className="p-3">
            <div className="relative group">
              <img
                src={previewUrl}
                alt="Post image"
                className="w-full h-48 object-cover rounded-md"
              />

              {/* Remove button */}
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={handleRemoveImage}
                disabled={disabled}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Section */}
      {!previewUrl && (
        <div className="space-y-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="post-image-upload" className="cursor-pointer">
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  disabled={uploading || disabled}
                  asChild
                >
                  <span>
                    {uploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    Add Image
                  </span>
                </Button>
              </div>
            </Label>
            <Input
              id="post-image-upload"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              disabled={uploading || disabled}
            />
          </div>

          {/* Help text */}
          <div className="text-sm text-muted-foreground">
            Add one image to your post. Supported formats: JPG, PNG, GIF, WebP (max 5MB)
          </div>
        </div>
      )}
    </div>
  );
}
