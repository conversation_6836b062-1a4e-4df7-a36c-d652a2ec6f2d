import React, { useState } from 'react';
import { Message<PERSON>ir<PERSON>, Edit, Trash2, MoreH<PERSON><PERSON><PERSON> } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { SocialService } from '../services/socialService';
import FollowButton from './FollowButton';
import PostReactions from './PostReactions';
import CommentSection from './CommentSection';
import UserProfileLink from './UserProfileLink';

import { NetZeroCategorySelector } from '@/components/netzero/NetZeroCategorySelector';
import { IndustrySelector } from '@/components/industries/IndustrySelector';
import type { SocialPostWithAuthor } from '../types/social.types';

interface SocialPostProps {
  post: SocialPostWithAuthor;
  onPostUpdate?: (postId: string) => void;
  onPostDelete?: (postId: string) => void;
  onReactionChange?: (postId: string) => void;
}

const SocialPost: React.FC<SocialPostProps> = ({
  post,
  onPostUpdate,
  onPostDelete,
  onReactionChange
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(post.content);
  const [editCategories, setEditCategories] = useState<string[]>(
    post.categories?.map(cat => cat.id) || []
  );
  const [editIndustries, setEditIndustries] = useState<string[]>(
    post.industries?.map(ind => ind.id) || []
  );
  const [showComments, setShowComments] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const isOwnPost = user?.id === post.user_id;

  const handleEditSave = async () => {
    const hasContentChanged = editContent.trim() !== post.content;
    const originalCategories = post.categories?.map(cat => cat.id) || [];
    const originalIndustries = post.industries?.map(ind => ind.id) || [];
    const hasCategoriesChanged = JSON.stringify(editCategories.sort()) !== JSON.stringify(originalCategories.sort());
    const hasIndustriesChanged = JSON.stringify(editIndustries.sort()) !== JSON.stringify(originalIndustries.sort());

    if (!editContent.trim()) {
      toast({
        title: "Error",
        description: "Post content cannot be empty.",
        variant: "destructive"
      });
      return;
    }

    if (!hasContentChanged && !hasCategoriesChanged && !hasIndustriesChanged) {
      setIsEditing(false);
      return;
    }

    try {
      setIsUpdating(true);
      await SocialService.updatePost(post.id, { 
        content: editContent.trim(),
        netZeroCategoryIds: editCategories,
        industryIds: editIndustries
      });
      
      setIsEditing(false);
      onPostUpdate?.(post.id);
      
      toast({
        title: "Post updated",
        description: "Your post has been updated successfully."
      });
    } catch (error) {
      console.error('Error updating post:', error);
      toast({
        title: "Error",
        description: "Failed to update post. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditContent(post.content);
    setEditCategories(post.categories?.map(cat => cat.id) || []);
    setEditIndustries(post.industries?.map(ind => ind.id) || []);
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await SocialService.deletePost(post.id);
      
      onPostDelete?.(post.id);
      setShowDeleteDialog(false);
      
      toast({
        title: "Post deleted",
        description: "Your post has been deleted successfully."
      });
    } catch (error) {
      console.error('Error deleting post:', error);
      toast({
        title: "Error",
        description: "Failed to delete post. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const toggleComments = () => {
    setShowComments(!showComments);
  };

  return (
    <>
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-start space-x-3">
            <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center overflow-hidden">
              {post.author.avatar_url ? (
                <img
                  src={post.author.avatar_url}
                  alt={`${post.author.first_name || 'User'} avatar`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-sm font-medium">
                  {post.author.first_name?.[0] || post.author.last_name?.[0] || 'U'}
                </span>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <UserProfileLink
                    userId={post.user_id}
                    firstName={post.author.first_name}
                    lastName={post.author.last_name}
                    jobTitle={post.author.job_title}
                    organisationName={post.author.organisation_name}
                    size="sm"
                  />
                  <span className="text-xs text-muted-foreground">
                    {new Date(post.created_at).toLocaleDateString()}
                    {post.edited_at && ' (edited)'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {user && user.id !== post.user_id && (
                    <FollowButton userId={post.user_id} />
                  )}
                  {isOwnPost && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal size={16} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setIsEditing(true)}>
                          <Edit size={16} className="mr-2" />
                          Edit Post
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => setShowDeleteDialog(true)}
                          className="text-destructive"
                        >
                          <Trash2 size={16} className="mr-2" />
                          Delete Post
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {/* Post Content */}
          {isEditing ? (
            <div className="mb-3 space-y-4">
              {/* Content editing */}
              <div>
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="min-h-[100px] resize-none"
                  maxLength={2000}
                  autoFocus
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-xs text-muted-foreground">
                    {editContent.length}/2000 characters
                  </span>
                </div>
              </div>

              {/* Category editing */}
              <div>
                <NetZeroCategorySelector
                  selectedSubcategories={editCategories}
                  onSelectionChange={setEditCategories}
                  title="Net-Zero Categories (optional)"
                  description="Update the categories for your post."
                  maxSelections={3}
                  allowPrimarySelection={false}
                />
              </div>

              {/* Industry editing */}
              <div>
                <IndustrySelector
                  selectedTargetIndustryIds={editIndustries}
                  onTargetIndustriesChange={setEditIndustries}
                  mode="multi"
                  title="Industries (optional)"
                  description="Update the industries for your post."
                  multiSelectLabel="Relevant Industries"
                  maxSelections={3}
                  allowParentSelection={false}
                />
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleEditCancel}
                  disabled={isUpdating}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleEditSave}
                  disabled={!editContent.trim() || isUpdating}
                >
                  {isUpdating ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </div>
          ) : (
            <>
              <p className="text-sm mb-3 whitespace-pre-wrap">{post.content}</p>

              {/* Post Image */}
              {post.image_url && (
                <div className="mb-3">
                  <img
                    src={post.image_url}
                    alt="Post image"
                    className="w-full max-h-96 object-cover rounded-lg"
                  />
                </div>
              )}
            </>
          )}

          {/* Categories and Industries */}
          {(post.categories?.length > 0 || post.industries?.length > 0) && (
            <div className="flex flex-wrap gap-1 mb-3">
              {post.categories?.map(category => (
                <Badge key={category.id} variant="secondary" className="text-xs">
                  {category.category.name}: {category.name}
                </Badge>
              ))}
              {post.industries?.map(industry => (
                <Badge key={industry.id} variant="outline" className="text-xs">
                  {industry.parent?.name ? `${industry.parent.name}: ` : ''}{industry.name}
                </Badge>
              ))}
            </div>
          )}

          <Separator className="my-3" />

          {/* Engagement Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 px-2"
                onClick={toggleComments}
              >
                <MessageCircle size={16} className="mr-1" />
                {post.comment_count || 0}
              </Button>
            </div>

            {/* Reactions */}
            <PostReactions
              post={post}
              variant="compact"
              onReactionChange={onReactionChange}
            />
          </div>
        </CardContent>
      </Card>

      {/* Comments Section */}
      {showComments && (
        <div className="ml-4 mb-4">
          <CommentSection postId={post.id} />
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Post</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this post? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default SocialPost;
