import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, X, Loader2, Image as ImageIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cloudflareService } from '@/services/cloudflareService';
import type { PostImage } from '../types/social.types';

interface SocialPostImageUploadProps {
  images: PostImage[];
  onImagesChange: (images: PostImage[]) => void;
  userId: string;
  maxImages?: number;
  disabled?: boolean;
}

export default function SocialPostImageUpload({
  images,
  onImagesChange,
  userId,
  maxImages = 4,
  disabled = false
}: SocialPostImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [newImageDescription, setNewImageDescription] = useState('');
  const { toast } = useToast();

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if we've reached the maximum number of images
    if (images.length >= maxImages) {
      toast({
        title: "Maximum images reached",
        description: `You can only upload up to ${maxImages} images per post.`,
        variant: "destructive"
      });
      return;
    }

    try {
      setUploading(true);

      toast({
        title: "Uploading...",
        description: "Your image is being uploaded to the cloud",
      });

      // Upload image to Cloudflare
      const { url, key } = await cloudflareService.uploadSocialPostImage(file, userId);

      // Create new image object
      const newImage: PostImage = {
        id: `img_${Date.now()}`,
        url,
        cloudflareKey: key,
        filename: file.name,
        description: newImageDescription.trim() || undefined,
        uploadedAt: new Date().toISOString(),
        order: images.length,
      };

      // Add to existing images
      const updatedImages = [...images, newImage];
      onImagesChange(updatedImages);

      // Clear description input
      setNewImageDescription('');

      toast({
        title: "Image uploaded",
        description: "Your image has been added to the post",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload image",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      // Reset the input
      event.target.value = '';
    }
  };

  const handleRemoveImage = async (imageToRemove: PostImage) => {
    try {
      // Delete from Cloudflare
      await cloudflareService.deleteSocialPostImage(imageToRemove.cloudflareKey);

      // Remove from local state
      const updatedImages = images
        .filter(img => img.id !== imageToRemove.id)
        .map((img, index) => ({ ...img, order: index })); // Reorder remaining images

      onImagesChange(updatedImages);

      toast({
        title: "Image removed",
        description: "The image has been removed from your post",
      });
    } catch (error) {
      console.error('Error removing image:', error);
      toast({
        title: "Error",
        description: "Failed to remove image. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDescriptionChange = (imageId: string, description: string) => {
    const updatedImages = images.map(img =>
      img.id === imageId ? { ...img, description: description.trim() || undefined } : img
    );
    onImagesChange(updatedImages);
  };

  return (
    <div className="space-y-4">
      {/* Upload Section */}
      {images.length < maxImages && (
        <div className="space-y-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="post-image-upload" className="cursor-pointer">
              <div className="flex items-center gap-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  disabled={uploading || disabled}
                  asChild
                >
                  <span>
                    {uploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    Add Image ({images.length}/{maxImages})
                  </span>
                </Button>
              </div>
            </Label>
            <Input
              id="post-image-upload"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              disabled={uploading || disabled}
            />
          </div>

          {/* Description input for next image */}
          <div className="max-w-md">
            <Textarea
              placeholder="Optional description for next image..."
              value={newImageDescription}
              onChange={(e) => setNewImageDescription(e.target.value)}
              className="text-sm"
              rows={2}
              maxLength={200}
              disabled={uploading || disabled}
            />
            <div className="text-xs text-muted-foreground mt-1">
              {newImageDescription.length}/200 characters
            </div>
          </div>
        </div>
      )}

      {/* Images Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {images.map((image) => (
            <Card key={image.id} className="relative">
              <CardContent className="p-3">
                <div className="relative group">
                  <img
                    src={image.url}
                    alt={image.description || image.filename}
                    className="w-full h-32 object-cover rounded-md"
                  />
                  
                  {/* Remove button */}
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleRemoveImage(image)}
                    disabled={disabled}
                  >
                    <X className="h-3 w-3" />
                  </Button>

                  {/* Image info overlay */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-2 rounded-b-md">
                    <div className="flex items-center gap-1">
                      <ImageIcon className="h-3 w-3" />
                      <span className="truncate">{image.filename}</span>
                    </div>
                  </div>
                </div>

                {/* Description input */}
                <div className="mt-2">
                  <Textarea
                    placeholder="Add a description..."
                    value={image.description || ''}
                    onChange={(e) => handleDescriptionChange(image.id, e.target.value)}
                    className="text-sm"
                    rows={2}
                    maxLength={200}
                    disabled={disabled}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    {(image.description || '').length}/200 characters
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Help text */}
      {images.length === 0 && (
        <div className="text-sm text-muted-foreground">
          Add up to {maxImages} images to your post. Supported formats: JPG, PNG, GIF, WebP (max 5MB each)
        </div>
      )}
    </div>
  );
}
